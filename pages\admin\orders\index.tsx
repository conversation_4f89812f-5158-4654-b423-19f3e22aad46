import AdminLayout from "@/components/features/admin/layout";
import { OrderStatusBadge } from "@/components/features/store/orders-table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { formatPrice } from "@/lib/utils";
import { formatId } from "@/lib/utils/order-helpers";
import { OrderStatusType } from "@/pages/api/orders/[id]/status";
import {
  useGetOrderQuery,
  useGetOrdersQuery,
  useUpdateOrderStatusMutation,
} from "@/queries/admin-queries";
import { useGetImage } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { Order, OrderStatus } from "@/supabase/types";
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {
  Check,
  ChevronDown,
  Copy,
  CreditCard,
  ExternalLink,
  FileText,
  Filter,
  Link as LinkIcon,
  Loader2,
  MoreHorizontal,
  Package,
  ShoppingCart,
  Truck,
  User,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";

interface AdminOrderData extends Order {
  order_statuses: OrderStatus[];
  order_items: Array<{
    id: string;
    quantity: number;
    options: Array<{
      name: string;
      value: string;
      price?: number;
    }>;
    products: {
      id: string;
      name: string;
      price: number;
      image?: string;
    };
  }>;
  shipping_addresses: {
    id?: string;
    contact_name?: string;
    address?: string;
    city?: string;
    state?: string;
    zip_code?: string;
  };
  totalItems: number;
  totalAmount: number;
  currentStatus: string;
  customer_data?: any;
}

const ORDER_STATUSES: OrderStatusType[] = [
  "pending",
  "awaiting_payment",
  "processing",
  "expired",
  "shipped",
  "delivered",
  "completed",
  "processed",
  "canceled",
  "denied",
  "canceled_reversal",
  "failed",
  "refunded",
  "reversed",
  "chargeback",
  "voided",
];

// Define the StatusCell as a standalone component
function StatusCell({ row, token }: { row: any; token: string }) {
  const order = row.original;
  const orderId = order.id;
  const trackingUrl = order.tracking_link;
  const currentStatus = order.currentStatus;
  const updateStatusMutation = useUpdateOrderStatusMutation(orderId, token);
  const [open, setOpen] = useState(false);
  const [showTrackingDialog, setShowTrackingDialog] = useState(false);
  const [trackingLink, setTrackingLink] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<OrderStatusType | null>(
    null
  );

  const handleStatusChange = async (status: OrderStatusType) => {
    // If changing to shipped status and no tracking link exists, show dialog
    if (status === "shipped" && !trackingUrl) {
      setSelectedStatus(status);
      setShowTrackingDialog(true);
      setOpen(false);
      return;
    }

    try {
      await updateStatusMutation.mutateAsync({ status });
      toast({
        title: "Order Status Updated",
        description: `Order status changed to ${status}`,
        variant: "success",
        duration: 3000,
      });
      setOpen(false);
    } catch (error: any) {
      toast({
        title: "Error Updating Order Status",
        description: `Failed to update status: ${
          error.message || "Unknown error"
        }`,
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleTrackingSubmit = async () => {
    if (!selectedStatus) return;

    try {
      await updateStatusMutation.mutateAsync({
        status: selectedStatus,
        trackingUrl: trackingLink,
      });
      toast({
        title: "Order Status Updated",
        description: `Order shipped with tracking information`,
        variant: "success",
        duration: 3000,
      });
      setShowTrackingDialog(false);
      setTrackingLink("");
      setSelectedStatus(null);
    } catch (error: any) {
      toast({
        title: "Error Updating Order Status",
        description: `Failed to update status: ${
          error.message || "Unknown error"
        }`,
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            type="button"
            variant="ghost"
            className="flex items-center justify-between h-8 px-3 py-1 hover:bg-transparent w-fit"
          >
            <OrderStatusBadge status={currentStatus} />
            <ChevronDown className="h-4 w-4 ml-2" />
            {updateStatusMutation.isPending ? (
              <Loader2 className="h-4 w-4 ml-auto animate-spin" />
            ) : null}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48 p-0">
          <ScrollArea className="h-60">
            {ORDER_STATUSES.map((status) => (
              <DropdownMenuItem
                key={status}
                className={`flex items-center gap-2 px-3 py-2 cursor-pointer ${
                  currentStatus === status ? "bg-accent" : ""
                }`}
                onClick={() => handleStatusChange(status)}
              >
                <div className="flex items-center gap-2 w-full">
                  <OrderStatusBadge status={status} />
                  {currentStatus === status && (
                    <Check className="h-4 w-4 ml-auto" />
                  )}
                </div>
              </DropdownMenuItem>
            ))}
          </ScrollArea>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={showTrackingDialog} onOpenChange={setShowTrackingDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Tracking Information</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="tracking-link">Tracking Link</Label>
              <div className="flex items-center gap-2">
                <LinkIcon className="h-4 w-4 text-muted-foreground" />
                <Input
                  id="tracking-link"
                  placeholder="https://tracking.carrier.com/ABC123"
                  value={trackingLink}
                  onChange={(e) => setTrackingLink(e.target.value)}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowTrackingDialog(false);
                setTrackingLink("");
                setSelectedStatus(null);
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleTrackingSubmit}
              disabled={updateStatusMutation.isPending}
            >
              {updateStatusMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Save & Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default function Orders() {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [statusFilter, setStatusFilter] = useState("");
  const [sorting, setSorting] = useState<SortingState>([
    { id: "created_at", desc: true },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState({});

  const { token, data: userData } = useAuthStore((state) => state);
  const sortBy = sorting.length > 0 ? sorting[0].id : "created_at";
  const sortOrder =
    sorting.length > 0 ? (sorting[0].desc ? "desc" : "asc") : "desc";

  const { data, isLoading, error } = useGetOrdersQuery(
    page,
    limit,
    token,
    searchTerm,
    statusFilter,
    sortBy,
    sortOrder
  );

  const [selectedOrder, setSelectedOrder] = useState<AdminOrderData | null>(
    null
  );
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Copy to clipboard function
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied to clipboard",
        description: "Text has been copied to your clipboard",
        variant: "success",
        duration: 2000,
      });
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Could not copy text to clipboard",
        variant: "destructive",
        duration: 2000,
      });
    }
  };

  // Function to handle server-side search
  const handleSearch = (value: string) => {
    // Only set searching state if the value has changed
    if (value !== searchTerm) {
      setIsSearching(true);
      setSearchTerm(value);
      setPage(1); // Reset to first page when searching
    }
  };

  // Function to handle status filter changes
  const handleStatusChange = (status: string) => {
    setStatusFilter(status);
    setPage(1); // Reset to first page when changing filters
  };

  // When new data arrives, clear the searching state
  useEffect(() => {
    if (isSearching && !isLoading) {
      setIsSearching(false);
    }
  }, [isLoading, isSearching]);

  const transformOrderData = (orders: any[]): AdminOrderData[] => {
    if (!orders) return [];

    return orders.map((order) => {
      const orderItems = order.order_items || [];
      const totalItems = orderItems.reduce(
        (acc: number, item: any) => acc + item.quantity,
        0
      );
      const totalAmount = order.total_amount;

      // Sort order_statuses by created_at to get the latest status
      if (order.order_statuses && Array.isArray(order.order_statuses)) {
        order.order_statuses.sort(
          (a: any, b: any) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      }

      const currentStatus = order.order_statuses?.[0]?.status || "pending";

      return {
        ...order,
        totalItems,
        totalAmount,
        currentStatus,
      };
    });
  };

  const tableData = useMemo(() => transformOrderData(data?.data || []), [data]);
  const total = data?.total || 0;
  const totalPages = data?.totalPages || 1;

  // Define columns with the token for the StatusCell
  const columns = useMemo<ColumnDef<AdminOrderData>[]>(
    () => [
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Company Name" />
        ),
        accessorKey: "customer_data.company_name",
        cell: ({ row }) => {
          const companyName =
            row.original?.customer_data?.company_name?.trim() || "N/A";

          return <div>{companyName}</div>;
        },
        enableSorting: true,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Order ID" />
        ),
        accessorKey: "id",
        cell: ({ row }) => {
          const id = row.original.id;
          const shortId = formatId(id);

          return (
            <div>
              <div className="group flex items-center gap-2">
                <span>{shortId}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => copyToClipboard(id)}
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
          );
        },
        enableSorting: false,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Customer Account#" />
        ),
        accessorKey: "customer_data.id",
        cell: ({ row }) => {
          const maxtonAccount =
            row?.original?.customer_data?.user_data?.business_details?.[0]?.maxton_account?.trim();

          const customerId =
            maxtonAccount !== "" && maxtonAccount !== undefined
              ? maxtonAccount
              : row?.original?.customer_id;

          const id = customerId;
          const shortId = formatId(customerId);

          return (
            <div className="group flex items-center gap-2">
              <span>{shortId}</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => copyToClipboard(customerId)}
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          );
        },
        enableSorting: false,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Customer" />
        ),
        accessorKey: "customer_data.user_data.email",
        cell: ({ row }) => {
          const email = row.original?.customer_data?.user_data?.email || "N/A";
          return <div>{email}</div>;
        },
        enableSorting: true,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Date" />
        ),
        accessorKey: "created_at",
        cell: ({ row }) => {
          const date = new Date(row.getValue("created_at"));
          return (
            <div className="w-32">
              {date.toLocaleDateString("en-US", {
                year: "numeric",
                month: "short",
                day: "numeric",
                hour: "numeric",
                minute: "numeric",
              })}
            </div>
          );
        },
        enableSorting: true,
      },
      {
        header: ({ column }) => {
          return <DataTableColumnHeader column={column} title="Last Updated" />;
        },
        accessorKey: "updated_at",
        cell: ({ row }) => {
          const createdAt = row.getValue("updated_at") as string;
          const date = new Date(createdAt);
          return (
            <div className="w-fit">
              {date.toLocaleDateString("en-US", {
                year: "numeric",
                month: "short",
                day: "numeric",
                hour: "numeric",
                minute: "numeric",
              })}
            </div>
          );
        },
        enableSorting: true,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Items" />
        ),
        accessorKey: "totalItems",
        cell: ({ row }) => {
          const totalItems = row.original.totalItems || 0;
          return <div>{totalItems}</div>;
        },
        enableSorting: true,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Total" />
        ),
        accessorKey: "totalAmount",
        cell: ({ row }) => {
          const amount = row.getValue("totalAmount") as number;
          return <div>{formatPrice(amount)}</div>;
        },
        enableSorting: true,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Status" />
        ),
        accessorKey: "currentStatus",
        cell: ({ row }) => <StatusCell row={row} token={token} />,
        enableSorting: true,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Tax Exempt" />
        ),
        accessorKey: "tax_exempt",
        cell: ({ row }) => {
          const taxExempt = row.original.tax_exempt as boolean | null;
          return <div>{taxExempt === true ? "Yes" : "No"}</div>;
        },
        enableSorting: true,
      },
      {
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Tracking" />
        ),
        accessorKey: "tracking_link",
        cell: ({ row }) => {
          const order = row.original;
          const trackingLink = order.tracking_link;
          if (!trackingLink) {
            return <div>No tracking link</div>;
          }
          return (
            <div>
              <Button variant="outline" asChild className="gap-2">
                <Link
                  href={trackingLink}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink className="h-4 w-4" />
                  Track Order
                </Link>
              </Button>
            </div>
          );
        },
        enableSorting: true,
      },
      {
        header: "Actions",
        id: "actions",
        meta: {
          className: "sticky right-0 bg-gray-100 dark:bg-zinc-950",
        },
        cell: ({ row }) => {
          const order = row.original;
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-full p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    setSelectedOrder(order);
                    setShowOrderDetails(true);
                  }}
                >
                  View details
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [token, copyToClipboard]
  );

  // Create a table instance with server-side pagination

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    pageCount: totalPages,
    manualPagination: true, // Tell the table we're handling pagination server-side
    manualFiltering: true, // Enable server-side filtering
    manualSorting: true, // Enable server-side sorting
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: (updater) => {
      setSorting(updater);
      // Reset to page 1 when sorting changes
      if (page !== 1) {
        setPage(1);
      }
    },
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize: limit,
        });
        setPage(newPagination.pageIndex + 1);
        setLimit(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setLimit(updater.pageSize);
      }
    },
    state: {
      sorting,
      rowSelection,
      columnFilters,
      pagination: {
        pageIndex: page - 1, // convert to 0-indexed for the table
        pageSize: limit,
      },
    },
  });

  return (
    <AdminLayout>
      <Head>
        <title>Sales</title>
      </Head>
      <div className="min-h-screen bg-white">
        <div className="flex-1 space-y-8 p-8 pt-6">
          {/* Header Section */}
          <div className="flex flex-col space-y-6">
            <div className="flex flex-col space-y-2">
              <h2 className="text-3xl font-bold tracking-tight">
                Orders Management
              </h2>
              <p className="text-muted-foreground">
                View and manage all customer orders in one place
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                <ShoppingCart className="mr-2 h-4 w-4" />
                <span className="text-primary">Total Orders: {total}</span>
              </Badge>
            </div>
          </div>

          {/* Orders Table Section */}
          <Card className="hover:shadow-md transition-all">
            <CardHeader>
              <CardTitle>Orders List</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Search and Filter Controls */}
              <div className="mb-4">
                <DataTableToolbar table={table}>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div>
                          <DataTableFilter
                            table={table as any}
                            column="id"
                            placeholder="Search..."
                            className="max-w-md min-w-80 h-12"
                            onSearch={handleSearch}
                            serverSideSearch={true}
                            debounceTime={300}
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="max-w-xs">
                        <p>
                          Search by full or partial order ID, email, company
                          name, or Maxton account number.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <OrderStatusFilter
                    value={statusFilter}
                    onChange={handleStatusChange}
                  />
                </DataTableToolbar>
              </div>

              {isLoading || isSearching ? (
                <DataTableSkeleton />
              ) : error ? (
                <div className="text-red-500 p-4">
                  Error loading orders: {error.message}
                </div>
              ) : (
                <DataTable data={tableData} columns={columns} table={table} />
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <Dialog open={showOrderDetails} onOpenChange={setShowOrderDetails}>
        <DialogContent className="max-w-4xl">
          <DialogHeader className="flex flex-row items-center justify-between px-6">
            <DialogTitle>
              Order Details - #
              {selectedOrder?.id ? formatId(selectedOrder.id) : "Loading..."}
            </DialogTitle>
          </DialogHeader>
          {selectedOrder && <OrderDetails order={selectedOrder} />}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}

interface OrderDetailsProps {
  order: AdminOrderData;
}

function OrderItem({ item }: { item: any }) {
  const options = item.options ?? [];
  const itemTotal = item.quantity * item.products.price;
  const { data: imageUrl } = useGetImage(item.products.image);

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-start gap-4">
        <div className="aspect-square h-16 w-16 rounded-lg">
          <img
            src={imageUrl || item.products.image || ""}
            alt={item.products.name}
            className="h-full w-full rounded-lg object-cover"
          />
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-sm font-medium">{item.products.name}</p>
          <p className="text-sm text-muted-foreground">
            Quantity: {item.quantity}
          </p>
          {options.map((option: any, index: number) => (
            <p
              key={`${item.id}-${option.name}-${option.value}-${index}`}
              className="text-sm text-muted-foreground"
            >
              {option.name}: {option.value}
              {Number(option.price) > 0 && (
                <span className="text-green-500">
                  {` (+${formatPrice(Number(option.price))})`}
                </span>
              )}
            </p>
          ))}
        </div>
      </div>
      <div className="flex items-end">
        <p className="text-sm font-medium">{formatPrice(itemTotal)}</p>
      </div>
    </div>
  );
}

function OrderItemSkeleton() {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-start gap-4">
        <Skeleton className="aspect-square h-16 w-16 rounded-lg" />
        <div className="flex flex-col gap-2 w-full">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-3 w-20" />
          <Skeleton className="h-3 w-32" />
        </div>
      </div>
      <Skeleton className="h-4 w-16" />
    </div>
  );
}

function OrderDetailsSkeleton() {
  return (
    <div className="grid gap-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-5 w-5 rounded-full" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-3 w-32" />
          </div>
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-5 w-24 rounded-full" />
        </div>
      </div>

      <Separator />

      <div className="flex flex-col gap-4">
        <Skeleton className="h-4 w-16" />
        <div className="grid gap-4">
          <OrderItemSkeleton />
          <OrderItemSkeleton />
        </div>
      </div>

      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-5 w-5 rounded-full" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-32" />
            <div className="space-y-1">
              <Skeleton className="h-3 w-40" />
              <Skeleton className="h-3 w-64" />
              <Skeleton className="h-3 w-48" />
            </div>
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-5 w-5 rounded-full" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-48" />
          </div>
        </div>
      </div>

      <Separator />

      <div className="flex flex-col gap-4">
        <div className="flex justify-between">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-20" />
        </div>
      </div>
    </div>
  );
}

function OrderDetails({ order }: OrderDetailsProps) {
  const orderId = order.id;
  const trackingLink = order.tracking_link;
  const token = useAuthStore((state) => state.token);
  const {
    data: orderData,
    isLoading,
    error: orderError,
  } = useGetOrderQuery(orderId, token);

  if (isLoading) return <OrderDetailsSkeleton />;
  if (orderError) return <div>Error loading order: {orderError.message}</div>;

  // Cast to enhanced type to access new fields
  const enhancedOrder = orderData;
  const items = enhancedOrder?.order_items ?? [];
  const shippingAddress = enhancedOrder?.shipping_addresses;
  const billingAddress = enhancedOrder?.billing_addresses;
  const customer = enhancedOrder?.customers;

  const user = enhancedOrder?.customers?.users;
  const date = enhancedOrder?.created_at;
  const dateFormat = new Intl.DateTimeFormat("en-US", {
    dateStyle: "medium",
    timeStyle: "short",
  });

  const dateString = date
    ? dateFormat.format(new Date(date))
    : new Date(date ?? "").toString();
  const totalAmount = enhancedOrder?.total_amount ?? 0;

  return (
    <ScrollArea className="max-h-2xl px-6">
      <div className="grid gap-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <ShoppingCart className="h-5 w-5 text-muted-foreground" />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Order Details</p>
              <p className="text-sm text-muted-foreground">{dateString}</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <p className="text-sm font-medium">
              Total: {formatPrice(totalAmount)}
            </p>
            <OrderStatusBadge status={order.currentStatus} />
          </div>
        </div>

        <Separator />

        {/* Payment Method Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <CreditCard className="h-5 w-5 text-muted-foreground" />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Payment Method</p>
              <div className="text-sm text-muted-foreground">
                {enhancedOrder?.payment_type
                  ? enhancedOrder.payment_type === "credit_card"
                    ? "Credit Card"
                    : enhancedOrder.payment_type === "purchase_order"
                    ? "Purchase Order"
                    : enhancedOrder.payment_type
                  : "Not specified"}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Customer Information Section */}
        {customer && user ? (
          <>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4">
                <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div className="flex flex-col gap-2">
                  <p className="text-sm font-medium">Customer Information</p>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>
                      <strong>Name:</strong> {user.first_name} {user.last_name}
                    </p>
                    <p>
                      <strong>Email:</strong> {user.email}
                    </p>
                    {user.phone && (
                      <p>
                        <strong>Phone:</strong> {user.phone}
                      </p>
                    )}
                    {customer.company_name && (
                      <p>
                        <strong>Company:</strong> {customer.company_name}
                      </p>
                    )}
                    {customer.company_website && (
                      <p>
                        <strong>Website:</strong> {customer.company_website}
                      </p>
                    )}
                    {customer.customer_number && (
                      <p>
                        <strong>Customer #:</strong> {customer.customer_number}
                      </p>
                    )}
                    {customer.primary_contact_name && (
                      <p>
                        <strong>Primary Contact:</strong>{" "}
                        {customer.primary_contact_name}
                      </p>
                    )}
                    {customer.role && (
                      <p>
                        <strong>Role:</strong> {customer.role}
                      </p>
                    )}
                    {/* Enhanced Business Details */}
                    {user.business_details &&
                      user.business_details.length > 0 && (
                        <>
                          {user.business_details[0].maxton_account && (
                            <p>
                              <strong>Maxton Account:</strong>{" "}
                              {user.business_details[0].maxton_account}
                            </p>
                          )}
                          {user.business_details[0].business_type && (
                            <p>
                              <strong>Business Type:</strong>{" "}
                              {user.business_details[0].business_type}
                            </p>
                          )}
                          {user.business_details[0].business_nature && (
                            <p>
                              <strong>Business Nature:</strong>{" "}
                              {user.business_details[0].business_nature}
                            </p>
                          )}
                          {user.business_details[0].authorized_contact_name && (
                            <p>
                              <strong>Authorized Contact:</strong>{" "}
                              {user.business_details[0].authorized_contact_name}
                            </p>
                          )}
                          {user.business_details[0].technical_contact && (
                            <p>
                              <strong>Technical Contact:</strong>{" "}
                              {user.business_details[0].technical_contact}
                              {user.business_details[0]
                                .technical_contact_phone &&
                                ` (${user.business_details[0].technical_contact_phone})`}
                            </p>
                          )}
                          {user.business_details[0].buyer_name && (
                            <p>
                              <strong>Buyer:</strong>{" "}
                              {user.business_details[0].buyer_name}
                              {user.business_details[0].buyer_phone &&
                                ` (${user.business_details[0].buyer_phone})`}
                            </p>
                          )}
                          {user.business_details[0].account_payable_contact && (
                            <p>
                              <strong>AP Contact:</strong>{" "}
                              {user.business_details[0].account_payable_contact}
                              {user.business_details[0].account_payable_phone &&
                              user.business_details[0].account_payable_phone
                                ? ` (${user.business_details[0].account_payable_phone})`
                                : null}
                            </p>
                          )}
                          {user.business_details[0].number_of_elevators !=
                          null ? (
                            <p>
                              <strong>Number of Elevators:</strong>{" "}
                              {user.business_details[0].number_of_elevators}
                            </p>
                          ) : null}
                          {user.business_details[0].has_mechanic && (
                            <p>
                              <strong>Has Mechanic:</strong>{" "}
                              {user.business_details[0].has_mechanic}
                            </p>
                          )}
                          {user.business_details[0].technician && (
                            <p>
                              <strong>Technician:</strong>{" "}
                              {user.business_details[0].technician}
                            </p>
                          )}
                        </>
                      )}
                  </div>
                </div>
              </div>
            </div>
            <Separator />
          </>
        ) : null}

        <div className="flex flex-col gap-4">
          <h3 className="text-sm font-medium">Items</h3>
          <ScrollArea className="h-fit min-h-[120px]">
            <div className="grid gap-4 pr-4">
              {items.length > 0 ? (
                items.map((item: any) => (
                  <OrderItem key={item.id} item={item} />
                ))
              ) : (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                      <svg
                        className="w-5 h-5 text-orange-600 mt-0.5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-orange-800 mb-1">
                        Some products in this order are no longer available.
                      </h4>
                      <p className="text-sm text-orange-700">
                        This order may have contained additional products that
                        are no longer available in our system.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>

        <Separator />

        {/* Shipping Address Section */}
        {shippingAddress && (
          <>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4">
                <Truck className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div className="flex flex-col gap-2">
                  <p className="text-sm font-medium">Shipping Address</p>
                  <div className="text-sm text-muted-foreground">
                    {shippingAddress.contact_name && (
                      <p>{shippingAddress.contact_name}</p>
                    )}
                    {shippingAddress.address && (
                      <p>{shippingAddress.address}</p>
                    )}
                    {(shippingAddress.city ||
                      shippingAddress.state ||
                      shippingAddress.zip_code) && (
                      <p>
                        {shippingAddress.city}
                        {shippingAddress.city && shippingAddress.state
                          ? ", "
                          : ""}
                        {shippingAddress.state} {shippingAddress.zip_code}
                      </p>
                    )}
                    {shippingAddress.country && (
                      <p>{shippingAddress.country}</p>
                    )}
                    {shippingAddress.contact_number && (
                      <p>Phone: {shippingAddress.contact_number}</p>
                    )}
                    {enhancedOrder?.purchase_order && (
                      <p>
                        <strong>PO Number:</strong>{" "}
                        {enhancedOrder.purchase_order}
                      </p>
                    )}
                  </div>
                  {trackingLink && (
                    <div className="mt-2">
                      <Link
                        href={trackingLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm inline-flex items-center gap-1.5 text-primary hover:underline"
                      >
                        <ExternalLink className="h-3.5 w-3.5" />
                        Track order
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <Separator />
          </>
        )}

        {/* Billing Address Section */}
        {billingAddress && (
          <>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4">
                <CreditCard className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div className="flex flex-col gap-2">
                  <p className="text-sm font-medium">Billing Address</p>
                  <div className="text-sm text-muted-foreground">
                    {billingAddress.company_name && (
                      <p>{billingAddress.company_name}</p>
                    )}
                    {billingAddress.address && <p>{billingAddress.address}</p>}
                    {(billingAddress.city ||
                      billingAddress.state ||
                      billingAddress.zip_code) && (
                      <p>
                        {billingAddress.city}
                        {billingAddress.city && billingAddress.state
                          ? ", "
                          : ""}
                        {billingAddress.state} {billingAddress.zip_code}
                      </p>
                    )}
                    {billingAddress.country && <p>{billingAddress.country}</p>}
                  </div>
                </div>
              </div>
            </div>
            <Separator />
          </>
        )}

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Package className="h-5 w-5 text-muted-foreground" />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Delivery Method</p>
              <div className="text-sm text-muted-foreground">
                {order.delivery_method}
                {order.ship_collect && (
                  <>
                    <br />
                    Ship Collect UPS
                    {order.ups_account_number && (
                      <>
                        <br />
                        Account Number: {order.ups_account_number}
                      </>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <FileText className="h-5 w-5 text-muted-foreground" />
            <div className="flex flex-col gap-2">
              <p className="text-sm font-medium">Tax Information</p>
              <div className="text-sm text-muted-foreground">
                {enhancedOrder?.tax_exempt ? "Tax Exempt" : "Taxable"}
                {(enhancedOrder as any)?.tax_rate &&
                (enhancedOrder as any).tax_rate > 0 ? (
                  <>
                    <br />
                    Tax Rate: {(enhancedOrder as any).tax_rate.toFixed(3)}%
                    <br />
                    Tax Amount:{" "}
                    {(enhancedOrder as any).tax_amount
                      ? formatPrice((enhancedOrder as any).tax_amount)
                      : "$0.00"}
                  </>
                ) : null}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Enhanced Customer Notes Section */}
        {(customer?.shipping_notes || user?.notes) && (
          <>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4">
                <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div className="flex flex-col gap-2">
                  <p className="text-sm font-medium">Customer Notes</p>
                  <div className="text-sm text-muted-foreground space-y-2">
                    {user?.notes && (
                      <div>
                        <p className="font-medium text-foreground">
                          General Customer Notes:
                        </p>
                        <p>{user.notes}</p>
                      </div>
                    )}
                    {customer?.shipping_notes && (
                      <div>
                        <p className="font-medium text-foreground">
                          Shipping Instructions:
                        </p>
                        <p>{customer.shipping_notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <Separator />
          </>
        )}

        {order?.notes ? (
          <>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4">
                <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div className="flex flex-col gap-2">
                  <p className="text-sm font-medium">Order Notes</p>
                  <div className="text-sm text-muted-foreground space-y-2">
                    <p>{order.notes}</p>
                  </div>
                </div>
              </div>
            </div>
            <Separator />
          </>
        ) : null}

        <div className="flex flex-col gap-4">
          {(enhancedOrder as any)?.tax_rate &&
          (enhancedOrder as any).tax_rate > 0 ? (
            <>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Subtotal</span>
                <span>
                  {formatPrice(
                    (totalAmount || 0) -
                      ((enhancedOrder as any).tax_amount || 0)
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  Tax ({(enhancedOrder as any).tax_rate.toFixed(3)}%)
                  {enhancedOrder?.tax_exempt && (
                    <span className="text-orange-600 ml-1">(Exempt)</span>
                  )}
                </span>
                <span
                  className={
                    enhancedOrder?.tax_exempt
                      ? "line-through text-muted-foreground"
                      : ""
                  }
                >
                  {enhancedOrder?.tax_exempt
                    ? "$0.00"
                    : formatPrice((enhancedOrder as any).tax_amount || 0)}
                </span>
              </div>
            </>
          ) : null}
          <div className="flex justify-between font-medium">
            <p className="text-sm">Total</p>
            <p className="text-sm font-medium">{formatPrice(totalAmount)}</p>
          </div>
        </div>
      </div>
    </ScrollArea>
  );
}

function OrderStatusFilter({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  const [open, setOpen] = useState(false);

  const handleStatusSelect = (status: OrderStatusType | "") => {
    onChange(status);
    setOpen(false);
  };

  // Get display text for the button
  const getButtonText = () => {
    if (!value) {
      return "All Statuses";
    }
    return value.charAt(0).toUpperCase() + value.slice(1);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={value ? "primary" : "outline"}
          size="default"
          className={`h-12 w-fit ${value ? "" : "border-dashed"}`}
        >
          <Filter className="h-4 w-4 mr-2" />
          {getButtonText()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px] p-0">
        <ScrollArea className="h-60">
          <DropdownMenuItem onClick={() => handleStatusSelect("")}>
            All Statuses
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {ORDER_STATUSES.map((status) => (
            <DropdownMenuItem
              key={status}
              className={value === status ? "bg-accent" : ""}
              onClick={() => handleStatusSelect(status)}
            >
              <OrderStatusBadge status={status} />
            </DropdownMenuItem>
          ))}
        </ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
