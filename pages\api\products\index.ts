import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Category, Product, ProductArrangement } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: getAllProductsHandler,
  POST: checkAdmin(checkPermission("create:products", createProductHandler)),
});

export interface ProductWithArrangementAndCategories extends Product {
  product_arrangements: ProductArrangement[];
  product_categories: Category[];
}

export interface GetAllProductsResponse {
  error?: string;
  products?: ProductWithArrangementAndCategories[];
  total?: number;
  totalPages?: number;
  totalFilteredProducts?: number;
  totalPagesFiltered?: number;
  categories?: any[];
}

async function getAllProductsHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetAllProductsResponse>
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;
  const categoriesFilter = query.category ? (query.category as string) : null;
  const search = query.search ? (query.search as string) : "";
  const minPrice = query.minPrice
    ? parseFloat(query.minPrice as string)
    : undefined;
  const maxPrice = query.maxPrice
    ? parseFloat(query.maxPrice as string)
    : undefined;
  const status = query.status ? (query.status as string) : null;
  const categories = categoriesFilter?.split(",") ?? undefined;

  const is_draft = status === "draft" ? true : undefined;
  const supabaseAdminClient = createSupabaseAdminClient();

  const { data: totalProducts } = await supabaseAdminClient
    .from("products")
    .select("id")
    .eq("draft", false);

  const { data: allProducts, error: categoriesError } =
    await supabaseAdminClient.rpc("get_products", {
      search,
      categories,
      min_price: minPrice,
      max_price: maxPrice,
      is_draft,
      page_limit: limit,
      page,
    });

  if (categoriesError) {
    console.log("Error fetching products:", categoriesError);
    return res.status(400).json({ error: "Failed to fetch products" });
  }

  const total = totalProducts?.length || allProducts?.length || 0;
  const totalPages = Math.ceil(total / limit);

  const totalFilteredProducts = allProducts?.length || 0;
  const totalPagesFiltered = Math.ceil(totalFilteredProducts / limit);

  return res.status(200).json({
    products: allProducts as unknown as ProductWithArrangementAndCategories[],
    total,
    totalPages,
    totalFilteredProducts,
    totalPagesFiltered,
  });
}

export const createProductSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  brand: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  price: z.number().min(0, "Price must be a positive number"),
  sku: z.string().nullable().optional(),
  available: z.boolean().default(true),
  is_quote: z.boolean().default(false),
  image: z.string().nullable().optional(),
  additional_images: z.array(z.string()).nullable().optional(),
  draft: z.boolean().default(false),
  variant: z.string().nullable().optional(),
  options: z.array(z.any()).nullable().optional(),
  specifications: z.any().nullable().optional(),
  helpful_hints: z.any().nullable().optional(),
  installation_instructions: z.any().nullable().optional(),
  delivery_and_shipping: z.any().nullable().optional(),
  warranty: z.any().nullable().optional(),
  user_id: z.string().nullable().optional(),
  category_id: z.string().nullable().optional(),
  group_prices: z
    .array(
      z.object({
        group_id: z.string(),
        custom_price: z.number(),
      })
    )
    .nullable()
    .optional(),
  tags: z.array(z.string()).nullable().optional(),
});

type CreateProductRequest = z.infer<typeof createProductSchema>;

export interface CreateProductResponse {
  error?: string;
  product?: Product;
}

async function createProductHandler(
  req: NextApiRequest,
  res: NextApiResponse<CreateProductResponse>
) {
  try {
    const validation = createProductSchema.safeParse(req.body);

    if (!validation.success) {
      return res.status(400).json({ error: validation.error.message });
    }

    const productData = validation.data;
    // Extract the category_id before creating the product
    const { category_id, group_prices, ...productFields } = productData;

    const supabaseAdminClient = createSupabaseAdminClient();

    // First, create the product
    const { data, error } = await supabaseAdminClient
      .schema("public")
      .from("products")
      .insert(productFields)
      .select()
      .single();

    if (error) {
      console.error("Error creating product:", error);
      return res.status(400).json({ error: error.message });
    }

    // If a category was selected, create the product_category relationship
    if (category_id && data) {
      const { error: categoryError } = await supabaseAdminClient
        .from("product_categories")
        .insert({
          product_id: data.id,
          category_id: category_id,
        });

      if (categoryError) {
        console.error(
          "Error creating product category relationship:",
          categoryError
        );
      }
    }

    if (group_prices && data) {
      const group_prices_data = group_prices.map((price: any) => ({
        product_id: data.id,
        group_id: price.group_id,
        custom_price: price.custom_price,
        hash: `${data.id}-${price.group_id}`,
      }));

      const { error: groupPriceError } = await supabaseAdminClient
        .from("product_group_prices")
        .insert(group_prices_data);

      if (groupPriceError) {
        console.error("Error creating product group prices:", groupPriceError);
      }
    }

    return res.status(201).json({ product: data });
  } catch (error) {
    return res.status(400).json({ error: "Failed to create product" });
  }
}
