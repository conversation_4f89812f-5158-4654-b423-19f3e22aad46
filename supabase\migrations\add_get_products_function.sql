-- sample usage
-- select
--   *
-- from
--   get_products (
--     '',
--     1,
--     100,
--     false,
--     10,
--     5000,
--     '{"Valve Kits"}'::text[]
--   )

create or replace function get_products (
  search text,
  page int default 1,
  page_limit int default 10,
  is_draft boolean default false,
  min_price numeric default null,
  max_price numeric default null,
  categories text[] default null
) returns table (
  id uuid,
  name varchar,
  description text,
  price numeric,
  brand varchar,
  sku varchar,
  draft boolean,
  available boolean,
  variant varchar,
  image text,
  options jsonb[],
  specifications jsonb,
  installation_instructions jsonb,
  helpful_hints jsonb,
  delivery_and_shipping jsonb,
  warranty jsonb,
  tags text[],
  additional_images text[],
  is_quote bool,
  created_at timestamptz,
  updated_at timestamptz,
  product_categories jsonb,
  product_arrangements jsonb
) language plpgsql as $$
     begin return query
    select
        p.id,
        p.name,
        p.description,
        p.price,
        p.brand,
        p.sku,
        p.draft,
        p.available,
        p.variant,
        p.image,
        p.options,
        p.specifications,
        p.installation_instructions,
        p.helpful_hints,
        p.delivery_and_shipping,
        p.warranty,
        p.tags,
        p.additional_images,
        p.is_quote,
        p.created_at,
        p.updated_at,
        pc.product_categories,
        pa.product_arrangements
    from
      products p
      left join lateral (
        select
          COALESCE(
            jsonb_agg(
              distinct to_jsonb(c) - 'created_at' - 'updated_at'
            ) filter (
              where
                pc.id is not null
            ),
            '[]'::jsonb
          ) as product_categories
        from
          product_categories pc
          left join categories c on c.id = pc.category_id 
        where
          pc.product_id = p.id
      ) pc on true
      left join lateral (
        select
          COALESCE(
            jsonb_agg(
              distinct to_jsonb(pa) - 'created_at' - 'updated_at'
            ) filter (
              where
                pa.id is not null
            ),
            '[]'::jsonb
          ) as product_arrangements
        from
          product_arrangements pa
        where
          pa.product_id = p.id
      ) pa on true
      where
        ( search is null or ( p.name ilike '%' || search || '%' or p.description ilike '%' || search || '%' ) )
        and ( p.draft = is_draft )
        and ( min_price is null or p.price >= min_price )
        and ( max_price is null or p.price <= max_price )
        and (categories is null or exists (select 1 from product_categories pc2 join categories c2 on c2.id = pc2.category_id where pc2.product_id = p.id and c2.name = any(categories) ))
      order by p.name
      limit page_limit
      offset (page - 1) * page_limit;
     end; $$;
