import { AddProductCategoryButton } from "@/components/features/admin/categories/add-product-category-button";
import { DeleteProductCategoryButton } from "@/components/features/admin/categories/delete-product-category-dialog";
import AdminLayout from "@/components/features/admin/layout";
import { ProductDraftFilter } from "@/components/features/admin/products/product-draft-filter";
import { ProductGroupPricesExport } from "@/components/features/admin/products/product-group-prices-export";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/shadcn-button";
import { formatPrice } from "@/lib/utils";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";
import { ProductWithArrangementAndCategories } from "@/pages/api/products";
import {
  useGetAllCategoriesQuery,
  useGetAllProductsQuery,
} from "@/queries/admin-queries";
import { useGetImage } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { Category, Product } from "@/supabase/types";
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  Table,
  useReactTable,
} from "@tanstack/react-table";
import {
  Filter,
  ImageIcon,
  MoreHorizontal,
  Pencil,
  PlusCircle,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function Products() {
  const permissions = useAuthStore((state) => state.permissions);
  const hasCreatePermission = checkUserHasPermission(
    permissions,
    "create:products"
  );

  return (
    <AdminLayout>
      <Head>
        <title>Products</title>
      </Head>
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold tracking-tight">Products</h1>
            <p className="text-sm text-muted-foreground">
              Manage your products here. Add, edit, save to draft, and publish.
            </p>
          </div>

          <div className="flex items-center gap-2">
            <ProductGroupPricesExport
              variant="outline"
              size="default"
              className="text-sm"
            />
            {hasCreatePermission && (
              <Button asChild className="bg-primary text-white">
                <Link href="/admin/products/new">
                  <PlusCircle className="w-4 h-4 mr-2" />
                  Add new product
                </Link>
              </Button>
            )}
          </div>
        </div>
        <ProductsTable />
      </div>
    </AdminLayout>
  );
}

// interface ProductDataTable extends Product {
//   product_categories?: {
//     category_data?: Category;
//   }[];
// }

const columns: ColumnDef<ProductWithArrangementAndCategories>[] = [
  {
    id: "select",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Select" />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "image",
    header: "Image",
    cell: ({ row }) => {
      const imagePath = row.getValue("image") as string;
      const imageUrl = imagePath.startsWith("http")
        ? imagePath
        : useGetImage(imagePath).data;

      return (
        <div className="flex h-12 w-12 items-center justify-center rounded-md overflow-hidden">
          <Avatar>
            {imageUrl ? (
              <AvatarImage src={imageUrl} />
            ) : (
              <AvatarFallback>
                <ImageIcon className="h-4 w-4" />
              </AvatarFallback>
            )}
          </Avatar>
        </div>
      );
    },
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Name" />;
    },
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue("name")}</div>;
    },
  },
  {
    accessorKey: "category",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Category" />;
    },
    cell: ({ row }) => {
      const category = row.original.product_categories?.at(0)?.name ?? "N/A";
      return <div>{category}</div>;
    },
  },
  {
    accessorKey: "tags",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Tags" />;
    },
    cell: ({ row }) => {
      const tags = row.getValue("tags") as string[];
      return <div>{tags?.join(", ")}</div>;
    },
    filterFn: (row, _column, filterValue) => {
      if (
        !filterValue ||
        !Array.isArray(filterValue) ||
        filterValue.length === 0
      )
        return true;
      return filterValue.some((tag) => row?.original?.tags?.includes(tag));
    },
  },
  {
    accessorKey: "price",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Price" />;
    },
    cell: ({ row }) => {
      const price = row.getValue("price") as number;
      return <div>{formatPrice(price)}</div>;
    },
    filterFn: (row, columnId, filterValue) => {
      // If no filter value or filterValue is empty, show all rows
      if (
        !filterValue ||
        !Array.isArray(filterValue) ||
        filterValue.length === 0
      )
        return true;

      const price = row.getValue(columnId) as number;

      // Check if price is within any of the selected ranges
      return filterValue.some((range) => {
        if (!range) return false;

        const { min, max } = range as {
          min: number | null;
          max: number | null;
        };

        if (min === null && max === null) return true;
        if (min === null && max !== null) return price <= max!;
        if (min !== null && max === null) return price >= min!;
        return price >= min! && price <= max!;
      });
    },
  },
  {
    accessorKey: "sku",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Product Code" />;
    },
    cell: ({ row }) => {
      const sku = row.getValue("sku") as string;
      return <div className="min-w-fit">{sku || "N/A"}</div>;
    },
  },
  {
    accessorKey: "draft",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Draft" />;
    },
    cell: ({ row }) => {
      const draft = row.getValue("draft") as boolean;
      return (
        <Badge
          variant={draft ? "primary" : "outline"}
          className="uppercase font-semibold tracking-wider"
        >
          {draft ? "Draft" : "Published"}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      return value === String(row.getValue(id));
    },
  },
  {
    accessorKey: "available",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Status" />;
    },
    cell: ({ row }) => {
      const available = row.original.available;
      const draft = row.original.draft;

      if (draft) {
        return (
          <Badge
            variant="primary"
            className="uppercase font-semibold tracking-wider"
          >
            Draft
          </Badge>
        );
      }

      return (
        <Badge
          variant={available ? "success" : "destructive"}
          className="uppercase font-semibold tracking-wider"
        >
          {available ? "Available" : "Unavailable"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "brand",
    header: "Brand",
    cell: ({ row }) => {
      const brand = row.getValue("brand") as string;
      return <div>{brand || "N/A"}</div>;
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Created At" />;
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("created_at"));
      const formatDate = Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
        hour12: true,
      }).format(date);

      return <div>{formatDate}</div>;
    },
  },
  {
    accessorKey: "updated_at",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Last Updated" />;
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("updated_at"));

      const formatTime = Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
        hour12: true,
      }).format(date);

      return <div>{formatTime}</div>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ProductTableActions,
    meta: {
      className: "sticky right-0 bg-gray-100 dark:bg-zinc-950",
    },
  },
];

interface ProductDataTableRowProps {
  row: Row<ProductWithArrangementAndCategories>;
}

function ProductTableActions({ row }: ProductDataTableRowProps) {
  const product = row.original;
  const [open, setOpen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const isDraft = product.draft;

  return (
    <>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Configure Product Categories</DialogTitle>
            <DialogDescription>
              Manage categories for {product.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AddProductCategoryButton
                  productId={product.id}
                  product={product}
                />
              </div>
            </div>
            <div className="border rounded-md">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="h-10 px-4 text-left">Category Name</th>
                    <th className="h-10 px-4 text-left">Value</th>
                    <th className="h-10 px-4 text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {product.product_categories?.map((cat: any) => (
                    <tr key={cat.id} className="border-b last:border-0">
                      <td className="px-4 py-2">{cat.name}</td>
                      <td className="px-4 py-2">{cat.value}</td>
                      <td className="px-4 py-2 text-right">
                        <DeleteProductCategoryButton
                          productId={product.id}
                          categoryId={cat.id}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-full p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem
            className="flex items-center justify-between"
            asChild
          >
            <Link href={`/admin/products/${product.sku}/edit`}>
              {isDraft ? "Continue Editing" : "Edit"}
              <Pencil className="h-4 w-4" />
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem
            className="flex items-center justify-between"
            onClick={() => {
              setOpen(false);
              setDialogOpen(true);
            }}
          >
            Configure Categories
          </DropdownMenuItem>
          {/* <DropdownMenuItem
                        className="flex items-center justify-between text-red-500"
                    >
                        Delete
                        <Trash className="h-4 w-4" />
                    </DropdownMenuItem> */}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}

function ProductsTable() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const token = useAuthStore((state) => state.token);

  // Create a table instance with server-side pagination
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState({});

  // Extract and transform filter values for server-side filtering
  const getFilterValues = () => {
    const priceFilter = columnFilters.find(f => f.id === "price")?.value as any[];
    const categoryFilter = columnFilters.find(f => f.id === "category")?.value as string[];
    const draftFilter = columnFilters.find(f => f.id === "draft")?.value as string;

    // Transform price filter to min/max values
    let minPrice: number | null = null;
    let maxPrice: number | null = null;

    if (priceFilter && priceFilter.length > 0) {
      // Find the overall min and max from all selected ranges
      const mins = priceFilter.map(range => range.min).filter(min => min !== null);
      const maxs = priceFilter.map(range => range.max).filter(max => max !== null);

      minPrice = mins.length > 0 ? Math.min(...mins) : null;
      maxPrice = maxs.length > 0 ? Math.max(...maxs) : null;
    }

    // Transform draft filter to status
    const status = draftFilter === "true" ? "draft" : null;

    return {
      categories: categoryFilter && categoryFilter.length > 0 ? categoryFilter : null,
      minPrice,
      maxPrice,
      status
    };
  };

  const { categories, minPrice, maxPrice, status } = getFilterValues();

  const { data, isLoading, isError } = useGetAllProductsQuery(
    page,
    pageSize,
    token,
    searchTerm,
    categories,
    minPrice,
    maxPrice,
    status
  );

  const products = data?.products || [];
  const totalItems = data?.total || 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  // Function to handle server-side search
  const handleSearch = (value: string) => {
    // Only set searching state if the value has changed
    if (value !== searchTerm) {
      setIsSearching(true);
      setSearchTerm(value);
      setPage(1); // Reset to first page when searching
    }
  };

  // When new data arrives, clear the searching state
  useEffect(() => {
    if (isSearching && !isLoading) {
      setIsSearching(false);
    }
  }, [isLoading, isSearching]);

  // Reset to first page when filters change
  useEffect(() => {
    setPage(1);
  }, [categories, minPrice, maxPrice, status]);

  const table = useReactTable({
    data: products,
    columns,
    pageCount: totalPages,
    manualPagination: true, // Tell the table we're handling pagination server-side
    manualFiltering: true, // Tell the table we're handling filtering server-side
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize,
        });
        setPage(newPagination.pageIndex + 1);
        setPageSize(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setPageSize(updater.pageSize);
      }
    },
    state: {
      sorting,
      rowSelection,
      columnFilters,
      pagination: {
        pageIndex: page - 1, // convert to 0-indexed for the table
        pageSize,
      },
    },
    debugTable: true,
  });

  return (
    <div className="space-y-4">
      {/* Always render the toolbar with the search input to maintain its state */}
      <DataTableToolbar table={table}>
        <DataTableFilter
          table={table as Table<any>}
          column="name"
          placeholder="Search products..."
          className="max-w-md min-w-80 h-12"
          onSearch={handleSearch}
          serverSideSearch={true}
          debounceTime={500}
        />
        <ProductPriceFilter table={table} />
        <ProductCategoryFilter table={table} />
        <ProductDraftFilter table={table} />
      </DataTableToolbar>

      {isLoading || isSearching ? (
        <DataTableSkeleton />
      ) : (
        <DataTable
          data={products}
          columns={columns}
          table={table}
          // Don't render the toolbar here since we've moved it outside
          renderToolbar={() => null}
        />
      )}
    </div>
  );
}

export function ProductPriceFilter({
  table,
}: {
  table: Table<ProductWithArrangementAndCategories>;
}) {
  const [selectedRanges, setSelectedRanges] = useState<string[]>(["all"]);

  const priceRanges = [
    { id: "all", label: "All", min: null, max: null },
    { id: "under-10", label: "Under $10", min: 0, max: 10 },
    { id: "10-50", label: "$10 - $50", min: 10, max: 50 },
    { id: "50-100", label: "$50 - $100", min: 50, max: 100 },
    { id: "100-200", label: "$100 - $200", min: 100, max: 200 },
    { id: "200-300", label: "$200 - $300", min: 200, max: 300 },
    { id: "300-plus", label: "$300+", min: 300, max: null },
  ];

  // Handle checkbox change
  const handleRangeChange = (rangeId: string, checked: boolean) => {
    let newRanges: string[];

    // If "All" is selected, clear all other filters
    if (rangeId === "all" && checked) {
      newRanges = ["all"];
    } else if (checked) {
      // If another range is selected, remove "All" and add the new range
      newRanges = [...selectedRanges.filter((id) => id !== "all"), rangeId];
    } else {
      // If unchecked, remove the range
      newRanges = selectedRanges.filter((id) => id !== rangeId);
      // If no ranges left, select "All"
      if (newRanges.length === 0) {
        newRanges = ["all"];
      }
    }

    setSelectedRanges(newRanges);

    // Apply filters to the table
    if (newRanges.includes("all")) {
      // Clear price filter if "All" is selected
      table.getColumn("price")?.setFilterValue(undefined);
    } else {
      // Filter products by the selected price ranges
      const selectedRangeObjects = newRanges
        .map((id) => priceRanges.find((r) => r.id === id))
        .filter(Boolean);

      table.getColumn("price")?.setFilterValue(selectedRangeObjects);
    }
  };

  // Get display text for the button
  const getButtonText = () => {
    if (selectedRanges.includes("all")) {
      return "All Prices";
    }

    if (selectedRanges.length === 1) {
      const range = priceRanges.find((r) => r.id === selectedRanges[0]);
      return range?.label || "Price";
    }

    return `${selectedRanges.length} price ranges`;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="default"
          className="hidden w-fit lg:flex h-12 border-dashed"
        >
          <Filter className="h-4 w-4 mr-2" />
          {getButtonText()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px] py-2">
        {priceRanges.map((range) => (
          <DropdownMenuItem
            key={range.id}
            className="pl-4"
            onSelect={(e) => {
              e.preventDefault();
              handleRangeChange(range.id, !selectedRanges.includes(range.id));
            }}
          >
            <Checkbox
              id={`price-range-${range.id}`}
              checked={selectedRanges.includes(range.id)}
              onCheckedChange={(checked) =>
                handleRangeChange(range.id, !!checked)
              }
            />
            <span className="ml-2">{range.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function ProductCategoryFilter({
  table,
}: {
  table: Table<ProductWithArrangementAndCategories>;
}) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  const token = useAuthStore((state) => state.token);
  const categories = useGetAllCategoriesQuery(1, 10, token);

  const handleCategoryChange = (category: string, checked: boolean) => {
    const newCategories = checked
      ? [...selectedCategories, category]
      : selectedCategories.filter((c) => c !== category);

    setSelectedCategories(newCategories);
    table
      .getColumn("category")
      ?.setFilterValue(newCategories.length ? newCategories : undefined);
  };

  const getButtonText = () => {
    if (selectedCategories.length === 0) return "All Categories";
    if (selectedCategories.length === 1) {
      const category = categories.data?.categories?.find(
        (c) => c.id === selectedCategories[0]
      );
      return category?.name || "Category";
    }
    return `${selectedCategories.length} categories`;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="default"
          className="hidden w-fit lg:flex h-12 border-dashed"
        >
          <Filter className="h-4 w-4 mr-2" />
          {getButtonText()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[250px] py-2">
        {categories.data?.categories?.map((category) => (
          <DropdownMenuItem
            key={category.id}
            className="pl-4"
            onSelect={(e) => {
              e.preventDefault();
              handleCategoryChange(
                category.id,
                !selectedCategories.includes(category.id)
              );
            }}
          >
            <Checkbox
              id={`category-${category.id}`}
              checked={selectedCategories.includes(category.id)}
              onCheckedChange={(checked) =>
                handleCategoryChange(category.id, !!checked)
              }
            />
            <span className="ml-2">{category.name}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
