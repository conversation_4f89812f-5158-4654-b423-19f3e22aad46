# API Documentation - Google Docs Compatible Version

## Overview
This document provides comprehensive API documentation for the Maxton Valve application. All endpoints require proper authentication unless otherwise specified.

**Base URL:** `https://your-domain.com`

**Authentication:** Bearer token in Authorization header

---

## Table of Contents

1. [Authentication Endpoints](#authentication-endpoints)
2. [User Management](#user-management)
3. [Product Endpoints](#product-endpoints)
4. [Category Endpoints](#category-endpoints)
5. [Customer Endpoints](#customer-endpoints)
6. [Order Management](#order-management)
7. [Address Management](#address-management)
8. [Group Management](#group-management)
9. [Discount Management](#discount-management)
10. [Admin Dashboard](#admin-dashboard)
11. [Tax Management](#tax-management)

---

## Authentication Endpoints

### POST /api/auth/sign-in
**Purpose:** Authenticate a user and create session

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| email | string | Yes | User email address |
| password | string | Yes | User password |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| access_token | string | JWT access token |
| access_token_expires_in | number | Token expiration time in seconds |
| access_token_expires_at | number | Token expiration timestamp |
| user | object | User information |
| role | string | User role (admin, customer, manager) |
| permissions | array | User permissions |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 400 (Invalid credentials), 401 (Unauthorized), 500 (Server error)

---

### POST /api/auth/refresh
**Purpose:** Refresh user session token

**Request:** Requires refresh token in cookies

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| access_token | string | New JWT access token |
| access_token_expires_in | number | Token expiration time |
| access_token_expires_at | number | Token expiration timestamp |
| user | object | User information |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 403 (Session expired), 500 (Server error)

---

### POST /api/auth/callback
**Purpose:** Handle email verification callback

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| access_token | string | Yes | Access token from email |
| expires_in | string | Yes | Token expiration |
| refresh_token | string | Yes | Refresh token |
| token_type | string | Yes | Token type |
| type | string | Yes | Callback type |

**Status Codes:** 200 (Success), 400 (Invalid token), 500 (Server error)

---

### POST /api/auth/forgot-password
**Purpose:** Initiate password reset process

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| email | string | Yes | User email address |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Operation success status |
| message | string | Success message |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 400 (Invalid email/Email not found), 500 (Server error)

---

## User Management

### GET /api/users
**Purpose:** Fetch all users (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| users | array | List of users |
| total | number | Total user count |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### GET /api/users/pending
**Purpose:** Fetch users with pending status (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| data | array | List of users |
| total | number | Total user count |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/users
**Purpose:** Create new user account

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| email | string | Yes | User email |
| password | string | Yes | User password |
| first_name | string | Yes | First name |
| last_name | string | Yes | Last name |
| phone | string | No | Phone number |

**Status Codes:** 200 (Success), 400 (Invalid data), 500 (Server error)

---

### PUT /api/users/[slug]/status
**Purpose:** Update user status (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| status | string | Yes | New user status |

**Status Codes:** 200 (Success), 400 (Invalid status), 401 (Unauthorized), 403 (Forbidden), 404 (User not found), 500 (Server error)

---

### GET /api/users/[userId]
**Purpose:** Fetch user data by ID
**Authentication:** Required

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| data | object | User data including phone and other fields |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 404 (User not found), 500 (Server error)

---

### GET /api/rejected-users
**Purpose:** Fetch rejected users (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

## Product Endpoints

### GET /api/products
**Purpose:** Fetch paginated public products

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |
| category | string | - | Filter by category |
| search | string | - | Search term |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| products | array | List of products |
| categories | array | Available categories |
| total | number | Total product count |
| totalPages | number | Total pages |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 500 (Server error)

---

### GET /api/products/[slug]
**Purpose:** Fetch single product by slug
**Note:** Updated from /:id to /[slug] to match actual implementation

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| product | object | Product details with group pricing and categories |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 404 (Product not found), 500 (Server error)

---

### POST /api/products
**Purpose:** Create new product (Admin only)
**Authentication:** Required (Admin with create:products permission)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | Yes | Product name |
| description | string | Yes | Product description |
| price | number | Yes | Product price |
| sku | string | Yes | Product SKU |
| stock | number | No | Stock quantity |
| options | array | No | Product options |
| images | array | No | Product images |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### PUT /api/products/[slug]
**Purpose:** Update existing product (Admin only)
**Authentication:** Required (Admin)
**Note:** This endpoint exists but was missing from original documentation

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Product not found), 500 (Server error)

---

### GET /api/customers/[userId]/products
**Purpose:** Fetch products for authenticated customer
**Authentication:** Required

**Query Parameters:** Same as GET /api/products

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/products/[slug]/categories
**Purpose:** Assign categories to product (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| category_ids | array | Yes | Array of category IDs |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Product not found), 500 (Server error)

---

### DELETE /api/products/[slug]/categories/[category_id]
**Purpose:** Remove category from product (Admin only)
**Authentication:** Required (Admin)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Product/category not found), 500 (Server error)

---

## Category Endpoints

### GET /api/categories
**Purpose:** Fetch all categories with pagination

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| categories | array | Categories with parent data |
| total | number | Total category count |
| totalPages | number | Total pages |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 500 (Server error)

---

### POST /api/categories
**Purpose:** Create new category (Admin only)
**Authentication:** Required (Admin with create:categories permission)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | Yes | Category name |
| value | string | Yes | Category value |
| description | string | No | Category description |
| parent_category_id | string | No | Parent category ID |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### PATCH /api/categories/[id]
**Purpose:** Update existing category (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | No | Category name |
| value | string | No | Category value |
| parent_category_id | string | No | Parent category ID |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Category not found), 500 (Server error)

---

### DELETE /api/categories/[id]
**Purpose:** Delete category (Admin only)
**Authentication:** Required (Admin with delete:categories permission)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Category not found), 500 (Server error)

---

## Customer Endpoints

### GET /api/customers
**Purpose:** Fetch all customers (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| customers | array | List of customers with categories and groups |
| total | number | Total customer count |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### GET /api/customers/[id]
**Purpose:** Fetch customer details
**Authentication:** Required (Owner or Admin)

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| customer | object | Customer data with group and categories |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### GET /api/customers/[customerId]/categories
**Purpose:** Fetch categories assigned to customer
**Authentication:** Required (Admin)

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| customer_categories | array | Assigned categories |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### POST /api/customers/[customerId]/categories
**Purpose:** Assign categories to customer (Admin only)
**Authentication:** Required (Admin with create:customer_categories permission)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| category_ids | array | Yes | Array of category IDs |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### DELETE /api/customers/[customerId]/categories/[categoryId]
**Purpose:** Remove category from customer (Admin only)
**Authentication:** Required (Admin with delete:customer_categories permission)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Customer/category not found), 500 (Server error)

---

### GET /api/customers/dashboard
**Purpose:** Fetch customer dashboard data
**Authentication:** Required

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| totalProducts | number | Total available products |
| totalOrders | number | Customer's total orders |
| recentOrders | array | Recent order data |
| memberSince | string | Account creation date |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

## Order Management

### GET /api/customers/[id]/orders
**Purpose:** Fetch customer's orders
**Authentication:** Required (Owner or Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| orders | array | Orders with status and items |
| total | number | Total order count |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### GET /api/customers/[id]/orders/[order_id]
**Purpose:** Fetch specific order details
**Authentication:** Required (Owner or Admin)

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| order | object | Complete order details with items |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Order not found), 500 (Server error)

---

### POST /api/orders
**Purpose:** Create new order
**Authentication:** Required

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| billingAddressId | string | Yes | Billing address ID |
| shippingAddressId | string | Yes | Shipping address ID |
| items | array | Yes | Order items with product_id, quantity, price |
| paymentType | string | Yes | Payment method |
| delivery_method | string | Yes | Delivery method |
| ship_collect | boolean | No | Ship collect option |
| ups_account_number | string | No | UPS account number |
| poNumber | string | No | Purchase order number |
| poFile | string | No | PO file attachment |
| tax_exempt | boolean | No | Tax exempt status |
| notes | string | No | Order notes |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| order | object | Created order details |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 404 (Product/address not found), 500 (Server error)

---

### PUT /api/orders/[id]/status
**Purpose:** Update order status (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| status | string | Yes | New order status |
| tracking_number | string | No | Tracking number |
| tracking_url | string | No | Tracking URL |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Operation success |
| message | string | Success message |
| order | object | Updated order |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 400 (Invalid status), 401 (Unauthorized), 403 (Forbidden), 404 (Order not found), 500 (Server error)

---

### PATCH /api/customers/[id]/orders/[order_id]
**Purpose:** Cancel order (Customer or Admin)
**Authentication:** Required (Owner or Admin)

**Status Codes:** 200 (Success), 400 (Cannot be cancelled), 401 (Unauthorized), 403 (Forbidden), 404 (Order not found), 500 (Server error)

---

## Address Management

### GET /api/customers/[id]/billing-address
**Purpose:** Fetch customer's billing addresses
**Authentication:** Required (Owner or Admin)

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| billing_address | array | List of billing addresses |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### POST /api/customers/[id]/billing-address
**Purpose:** Add new billing address
**Authentication:** Required (Owner or Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| address | string | Yes | Address line 1 |
| address2 | string | No | Address line 2 |
| city | string | Yes | City |
| state | string | Yes | State |
| zip_code | string | Yes | ZIP code |
| country | string | Yes | Country |
| company_name | string | No | Company name |
| address_type | string | No | Address type |
| effective_date | string | No | Effective date |
| note | string | No | Additional notes |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### PATCH /api/customers/[id]/billing-address/[billing_id]
**Purpose:** Update billing address
**Authentication:** Required (Owner or Admin)

**Fields:** Same as POST (all optional)

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

### DELETE /api/customers/[id]/billing-address/[billing_id]
**Purpose:** Delete billing address
**Authentication:** Required (Owner or Admin)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

### GET /api/customers/[id]/shipping-address
**Purpose:** Fetch customer's shipping addresses
**Authentication:** Required (Owner or Admin)

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| shipping_address | array | List of shipping addresses |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### POST /api/customers/[id]/shipping-address
**Purpose:** Add new shipping address
**Authentication:** Required (Owner or Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| address_line_1 | string | Yes | Address line 1 |
| address_line_2 | string | No | Address line 2 |
| city | string | Yes | City |
| state | string | Yes | State |
| postal_code | string | Yes | Postal code |
| country | string | Yes | Country |
| is_default | boolean | No | Default address flag |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### PATCH /api/customers/[id]/shipping-address/[shipping_id]
**Purpose:** Update shipping address
**Authentication:** Required (Owner or Admin)

**Fields:** Same as POST (all optional)

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

### DELETE /api/customers/[id]/shipping-address/[shipping_id]
**Purpose:** Delete shipping address
**Authentication:** Required (Owner or Admin)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

### GET /api/billing-addresses/pending
**Purpose:** Fetch pending billing addresses (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| billing_addresses | array | Pending billing addresses |
| total | number | Total count |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/billing-addresses/pending
**Purpose:** Approve/deny pending billing address (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| address_id | string | Yes | Address ID |
| status | string | Yes | 'approved' or 'denied' |
| notes | string | No | Admin notes |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

## Group Management

### GET /api/groups
**Purpose:** Fetch all groups (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| groups | array | Groups with member data |
| total | number | Total group count |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/groups
**Purpose:** Create new group (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | Yes | Group name |
| description | string | No | Group description |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

## Discount Management

### GET /api/discounts
**Purpose:** Fetch all discounts (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| discounts | array | List of discounts |
| total | number | Total discount count |
| totalPages | number | Total pages |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/discounts
**Purpose:** Create new discount
**Authentication:** Required

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| code | string | Yes | Discount code |
| description | string | No | Discount description |
| discount_percentage | number | Yes | Discount percentage |

**Status Codes:** 200 (Success), 400 (Invalid request), 500 (Server error)

---

## Admin Dashboard

### GET /api/admin/dashboard
**Purpose:** Fetch admin dashboard data
**Authentication:** Required (Admin)

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| totalSales | number | Total sales amount |
| totalOrders | number | Total order count |
| totalCustomers | number | Total customer count |
| totalPendingCustomers | number | Pending customer count |
| totalProducts | number | Total product count |
| totalDiscounts | number | Total discount count |
| salesData | array | Monthly sales data |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

## Tax Management

### PUT /api/tax-rates/[id]
**Purpose:** Update tax rate for specific city (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| tax_rate | number | Yes | New tax rate |

**Response:**
| Field | Type | Description |
|-------|------|-------------|
| taxRate | object | Updated tax rate data |
| error | string | Error message if failed |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Tax rate not found), 500 (Server error)

---

## Status Codes Reference

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid data or parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 405 | Method Not Allowed |
| 500 | Internal Server Error |

---

## Authentication Notes

1. **Bearer Token:** Include in Authorization header as `Bearer <token>`
2. **Session Management:** Tokens expire and need refresh via `/api/auth/refresh`
3. **Permissions:** Some endpoints require specific permissions beyond role-based access
4. **Admin Access:** Admin endpoints require admin role and proper permissions

---

## Updates Made in This Version

**Corrections from Original Documentation:**
1. Updated `/api/products/:id` to `/api/products/[slug]` to match actual implementation
2. Added missing `PUT /api/products/[slug]` endpoint for product updates
3. Added missing `GET /api/users/pending` endpoint
4. Added missing `GET /api/rejected-users` endpoint
5. Added missing category management endpoints (`PATCH` and `DELETE /api/categories/[id]`)
6. Added missing order status update endpoint (`PUT /api/orders/[id]/status`)
7. Added missing individual order endpoint (`GET /api/customers/[id]/orders/[order_id]`)
8. Added missing tax rate management endpoint (`PUT /api/tax-rates/[id]`)
9. Corrected billing address endpoints to match actual implementation
10. Updated response structures to match actual API responses

**Format Improvements for Google Docs:**
1. Used tables instead of code blocks for better readability
2. Simplified formatting for Google Docs compatibility
3. Added clear section dividers
4. Organized information in a more structured way
5. Added comprehensive status codes reference
6. Added authentication notes section

---

*Last Updated: [Current Date]*
*Version: 2.0 - Google Docs Compatible*
